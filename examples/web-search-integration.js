/**
 * @fileoverview Example of integrating Web Search with existing chat functionality
 */

import express from 'express';
import { WebSearchService } from '../src/services/WebSearchService.js';
import { ChatService } from '../src/services/ChatService.js';
import { PineconeService } from '../src/services/PineconeService.js';

const app = express();
app.use(express.json());

/**
 * Example: Enhanced chat endpoint that can trigger web search
 */
app.post('/api/chat/enhanced', async (req, res) => {
  try {
    const { message, sessionId, userId, enableWebSearch = false } = req.body;

    // Check if message contains web search triggers
    const webSearchTriggers = [
      'search for',
      'find information about',
      'latest news on',
      'current information about',
      'recent developments in',
      'what\'s happening with'
    ];

    const shouldPerformWebSearch = enableWebSearch || 
      webSearchTriggers.some(trigger => 
        message.toLowerCase().includes(trigger.toLowerCase())
      );

    if (shouldPerformWebSearch && WebSearchService.isAvailable()) {
      // Extract search query from message
      const searchQuery = extractSearchQuery(message);
      
      if (searchQuery) {
        console.log(`Triggering web search for: "${searchQuery}"`);
        
        // Perform web search and get streaming response
        const searchRequest = {
          query: searchQuery,
          sessionId,
          llmModel: 'gpt-4o'
        };

        if (userId) {
          await WebSearchService.processUserWebSearchStreaming(
            userId,
            searchRequest,
            res
          );
        } else {
          await WebSearchService.processGuestWebSearchStreaming(
            searchRequest,
            res,
            null,
            req.ip
          );
        }
        return;
      }
    }

    // Fallback to regular chat if no web search needed
    const chatRequest = {
      message,
      sessionId,
      llmModel: 'gpt-4o'
    };

    if (userId) {
      await ChatService.processSimpleUserChatStreaming(
        userId,
        chatRequest,
        res
      );
    } else {
      await ChatService.processSimpleGuestChatStreaming(
        chatRequest,
        res,
        null,
        req.ip
      );
    }

  } catch (error) {
    console.error('Error in enhanced chat:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process chat message'
    });
  }
});

/**
 * Example: Smart chat that combines web search with existing knowledge
 */
app.post('/api/chat/smart', async (req, res) => {
  try {
    const { message, sessionId, userId } = req.body;

    // Set up streaming headers
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
    });

    // Step 1: Check existing knowledge in Pinecone
    res.write(`data: ${JSON.stringify({
      type: 'status',
      message: 'Searching existing knowledge base...',
      timestamp: new Date().toISOString()
    })}\n\n`);

    const existingContext = await PineconeService.searchRelevantContent(
      sessionId, 
      message, 
      3
    );

    // Step 2: Determine if web search is needed
    const needsWebSearch = shouldTriggerWebSearch(message, existingContext);

    if (needsWebSearch && WebSearchService.isAvailable()) {
      res.write(`data: ${JSON.stringify({
        type: 'status',
        message: 'Searching the web for latest information...',
        timestamp: new Date().toISOString()
      })}\n\n`);

      // Perform web search
      const searchResults = await WebSearchService.performSearch(message);
      
      // Store results in Pinecone
      await WebSearchService.storeSearchResultsInPinecone(
        sessionId, 
        message, 
        await processSearchResults(searchResults)
      );

      res.write(`data: ${JSON.stringify({
        type: 'status',
        message: 'Combining web search results with existing knowledge...',
        timestamp: new Date().toISOString()
      })}\n\n`);
    }

    // Step 3: Generate enhanced response
    const enhancedPrompt = createEnhancedPrompt(message, existingContext, needsWebSearch);
    
    // Continue with regular LLM streaming...
    // (Implementation would continue with LLMService.generateStreamingResponse)

  } catch (error) {
    console.error('Error in smart chat:', error);
    res.write(`data: ${JSON.stringify({
      type: 'error',
      error: error.message,
      timestamp: new Date().toISOString()
    })}\n\n`);
    res.end();
  }
});

/**
 * Example: Batch web search for multiple queries
 */
app.post('/api/websearch/batch', async (req, res) => {
  try {
    const { queries, sessionId, userId } = req.body;

    if (!Array.isArray(queries) || queries.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Queries array is required'
      });
    }

    if (queries.length > 5) {
      return res.status(400).json({
        success: false,
        message: 'Maximum 5 queries allowed per batch'
      });
    }

    const results = [];

    for (const query of queries) {
      try {
        const searchResults = await WebSearchService.performSearch(query);
        const processedResults = await processSearchResults(searchResults);
        
        results.push({
          query,
          success: true,
          results: processedResults,
          count: processedResults.length
        });

        // Store in Pinecone for future reference
        await WebSearchService.storeSearchResultsInPinecone(
          sessionId,
          query,
          processedResults
        );

      } catch (error) {
        results.push({
          query,
          success: false,
          error: error.message
        });
      }
    }

    res.json({
      success: true,
      data: {
        sessionId,
        results,
        totalQueries: queries.length,
        successfulQueries: results.filter(r => r.success).length
      }
    });

  } catch (error) {
    console.error('Error in batch web search:', error);
    res.status(500).json({
      success: false,
      message: 'Batch search failed'
    });
  }
});

/**
 * Helper function to extract search query from user message
 */
function extractSearchQuery(message) {
  // Simple extraction logic - can be enhanced with NLP
  const patterns = [
    /search for (.+)/i,
    /find information about (.+)/i,
    /latest news on (.+)/i,
    /current information about (.+)/i,
    /recent developments in (.+)/i,
    /what's happening with (.+)/i
  ];

  for (const pattern of patterns) {
    const match = message.match(pattern);
    if (match) {
      return match[1].trim();
    }
  }

  // If no specific pattern, use the whole message as query
  return message.length > 100 ? message.substring(0, 100) : message;
}

/**
 * Helper function to determine if web search is needed
 */
function shouldTriggerWebSearch(message, existingContext) {
  // Trigger web search if:
  // 1. No existing context found
  // 2. Message contains time-sensitive keywords
  // 3. Message asks for recent/current information

  if (!existingContext || existingContext.length === 0) {
    return true;
  }

  const timeSensitiveKeywords = [
    'latest', 'recent', 'current', 'today', 'now', 'new',
    'breaking', 'update', 'news', '2024', '2025'
  ];

  return timeSensitiveKeywords.some(keyword => 
    message.toLowerCase().includes(keyword)
  );
}

/**
 * Helper function to create enhanced prompt
 */
function createEnhancedPrompt(message, existingContext, hasWebSearch) {
  let prompt = `User Question: ${message}\n\n`;

  if (existingContext && existingContext.length > 0) {
    prompt += `Existing Knowledge:\n`;
    existingContext.forEach((context, index) => {
      prompt += `${index + 1}. ${context.metadata.content}\n`;
    });
    prompt += '\n';
  }

  if (hasWebSearch) {
    prompt += `Note: Latest web search results have been added to the knowledge base.\n\n`;
  }

  prompt += `Please provide a comprehensive answer based on the available information.`;

  return prompt;
}

// Start example server
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`Web Search Integration Examples running on port ${PORT}`);
  console.log('Available endpoints:');
  console.log('- POST /api/chat/enhanced - Enhanced chat with auto web search');
  console.log('- POST /api/chat/smart - Smart chat combining existing and web knowledge');
  console.log('- POST /api/websearch/batch - Batch web search for multiple queries');
});
