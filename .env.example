# Server Configuration
PORT=5529
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=24h

# User Database Configuration
USER_DB_HOST=localhost
USER_DB_PORT=3306
USER_DB_NAME=infini_ai_users
USER_DB_USERNAME=inf_ai_user
USER_DB_PASSWORD=inf_ai_user

# Chat Database Configuration
CHAT_DB_HOST=localhost
CHAT_DB_PORT=3306
CHAT_DB_NAME=infini_ai_user_chat_recs
CHAT_DB_USERNAME=inf_ai_chat_recs
CHAT_DB_PASSWORD=inf_ai_chat_recs

# LLM Configuration
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
DEEPSEEK_API_KEY=your_deepseek_api_key_here
TOGETHER_API_KEY=your_together_api_key_here
META_API_KEY=your_meta_api_key_here
DEFAULT_LLM_MODEL=gpt-3.5-turbo
META_API_BASE_URL=https://api.together.xyz/v1

# OTP Configuration
OTP_EXPIRY_MINUTES=5

# Email/SMTP Configuration (GoDaddy)
SMTP_HOST=smtpout.secureserver.net
SMTP_PORT=465
SMTP_SECURE=true
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_password_here
SMTP_FROM_EMAIL=<EMAIL>

# Security
CSRF_SECRET=your_csrf_secret_here

# Guest Chat Limits
GUEST_CHAT_LIMIT=5

# Pinecone Configuration
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_ENVIRONMENT=your_pinecone_environment_here
PINECONE_INDEX_NAME=theinfini-ai-chat

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_REGION=us-east-1
AWS_S3_BUCKET=theinfini-ai-attachments
AWS_S3_FOLDER_PREFIX=chat-attachments
