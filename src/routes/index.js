import { Router  } from 'express';
import { ResponseUtil  } from '../utils/response.js';
import authRoutes from './auth.js';
import chatRoutes from './chat.js';
import userRoutes from './user.js';
import threadRoutes from './threads.js';
import projectRoutes from './projects.js';
import fileRoutes from './files.js';
import subscriptionRoutes from './subscription.js';
import paymentRoutes from './payment.js';
import invoiceRoutes from './invoices.js';
import supportRoutes from './support.js';
import llmModelRoutes from './llmModels.js';
import webSearchRoutes from './webSearch.js';

const router = Router();

// Health check endpoint
router.get('/health', (req, res) => {
  ResponseUtil.success(res, 'Server is healthy', {
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: process.env.npm_package_version || '1.0.0',
  });
});

// API version info
router.get('/version', (req, res) => {
  ResponseUtil.success(res, 'API version information', {
    version: '1.0.0',
    name: 'The Infini AI Backend',
    description: 'AI Chat Backend with Node.js, JavaScript, and Langchain',
    author: 'The Infini Team',
  });
});

// Mount route modules
router.use('/auth', authRoutes);
router.use('/chat', chatRoutes);
router.use('/user', userRoutes);
router.use('/threads', threadRoutes);
router.use('/projects', projectRoutes);
router.use('/files', fileRoutes);
router.use('/subscription', subscriptionRoutes);
router.use('/payment', paymentRoutes);
router.use('/invoices', invoiceRoutes);
router.use('/support', supportRoutes);
router.use('/llm-models', llmModelRoutes);
router.use('/websearch', webSearchRoutes);

export default router;
