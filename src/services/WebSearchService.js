/**
 * @fileoverview Web Search Service using <PERSON><PERSON><PERSON><PERSON> for context-aware search
 */

import { TavilySearch } from '@langchain/tavily';
import logger from '../config/logger.js';
import { PineconeService } from './PineconeService.js';
import { LLMService } from './LLMService.js';
import { CreditService } from './CreditService.js';
import { ChatMessage } from '../models/chat/ChatMessage.js';
import { Chat } from '../models/chat/Chat.js';
import { 
  processSearchResults, 
  formatSearchResultsForLLM, 
  generateWebSearchNamespace,
  validateSearchQuery 
} from '../utils/webSearchUtils.js';
import { WEB_SEARCH_SYSTEM, CREDIT_SYSTEM } from '../utils/constants.js';

export class WebSearchService {
  static tavilyTool = null;

  /**
   * Initialize Tavily search tool
   */
  static async initialize() {
    try {
      if (!process.env.TAVILY_API_KEY) {
        logger.warn('Tavily API key not found. Web search service will be disabled.');
        return;
      }

      this.tavilyTool = new TavilySearch({
        maxResults: WEB_SEARCH_SYSTEM.MAX_SEARCH_RESULTS,
        topic: 'general',
        includeAnswer: false,
        includeRawContent: false,
        includeImages: false,
        searchDepth: 'basic',
      });

      logger.info('Tavily web search service initialized successfully');
    } catch (error) {
      logger.error('Error initializing Tavily web search service:', error);
      throw error;
    }
  }

  /**
   * Check if web search service is available
   * @returns {boolean} Whether web search is available
   */
  static isAvailable() {
    return !!this.tavilyTool && !!process.env.TAVILY_API_KEY;
  }

  /**
   * Perform web search and get results
   * @param {string} query - Search query
   * @returns {Promise<Array>} Search results
   */
  static async performSearch(query) {
    try {
      if (!this.isAvailable()) {
        throw new Error('Web search service is not available');
      }

      const validation = validateSearchQuery(query);
      if (!validation.isValid) {
        throw new Error(validation.error);
      }

      logger.info(`Performing web search for query: "${validation.query}"`);

      const searchResults = await this.tavilyTool.invoke({
        query: validation.query,
      });

      // Handle different response formats from Tavily
      let results = [];
      if (typeof searchResults === 'string') {
        // If Tavily returns a string, try to parse it as JSON
        try {
          const parsed = JSON.parse(searchResults);
          results = Array.isArray(parsed) ? parsed : (parsed.results || []);
        } catch (parseError) {
          logger.warn('Failed to parse Tavily string response, treating as single result');
          results = [{
            title: 'Search Result',
            url: '#',
            content: searchResults,
            score: 1.0
          }];
        }
      } else if (Array.isArray(searchResults)) {
        results = searchResults;
      } else if (searchResults && typeof searchResults === 'object') {
        // Handle object response with results property
        results = searchResults.results || searchResults.data || [searchResults];
      } else {
        logger.warn('Unexpected Tavily response format:', typeof searchResults);
        results = [];
      }

      logger.debug(`Tavily returned ${results.length} search results`);
      return results;
    } catch (error) {
      logger.error('Error performing web search:', error);
      throw new Error(`Web search failed: ${error.message}`);
    }
  }

  /**
   * Process web search with streaming response for authenticated users
   * @param {string} userId - User ID
   * @param {Object} searchRequest - Search request object
   * @param {Object} res - Express response object
   * @param {Object} [attachedFile] - Optional attached file
   * @returns {Promise<Object>} Search result with metadata
   */
  static async processUserWebSearchStreaming(userId, searchRequest, res, attachedFile = null) {
    try {
      const { query, sessionId, llmModel } = searchRequest;

      logger.info(`Processing web search for user ${userId}, query: "${query.substring(0, 50)}..."`);

      // Validate query
      const validation = validateSearchQuery(query);
      if (!validation.isValid) {
        throw new Error(validation.error);
      }

      // Check user credits
      const hasCredits = await CreditService.checkCredits(userId, CREDIT_SYSTEM.WEB_SEARCH_COST);
      if (!hasCredits) {
        throw new Error('Insufficient credits for web search');
      }

      // Get or create chat session
      let chat = await Chat.findBySessionId(sessionId);
      if (!chat) {
        chat = await Chat.createChat({
          userId,
          sessionId,
          title: `Web Search: ${validation.query.substring(0, 50)}...`,
        });
      }

      // Set up streaming headers
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
      });

      // Send initial status
      res.write(`data: ${JSON.stringify({
        type: 'status',
        message: 'Starting web search...',
        timestamp: new Date().toISOString()
      })}\n\n`);

      // Perform web search
      const searchResults = await this.performSearch(validation.query);
      
      res.write(`data: ${JSON.stringify({
        type: 'status',
        message: `Found ${searchResults.length} search results, extracting content...`,
        timestamp: new Date().toISOString()
      })}\n\n`);

      // Process search results and extract content
      const processedResults = await processSearchResults(searchResults);
      
      res.write(`data: ${JSON.stringify({
        type: 'status',
        message: 'Storing search results in knowledge base...',
        timestamp: new Date().toISOString()
      })}\n\n`);

      // Store search results in Pinecone
      await this.storeSearchResultsInPinecone(sessionId, validation.query, processedResults);

      res.write(`data: ${JSON.stringify({
        type: 'status',
        message: 'Generating AI response...',
        timestamp: new Date().toISOString()
      })}\n\n`);

      // Format search results for LLM context
      const searchContext = formatSearchResultsForLLM(processedResults);
      
      // Create enhanced prompt with search context
      const enhancedPrompt = `${WEB_SEARCH_SYSTEM.SYSTEM_PROMPT}

Search Results:
${searchContext}

User Question: ${validation.query}

Please provide a comprehensive answer based on the search results above.`;

      // Generate streaming response using LLM
      const response = await LLMService.generateStreamingResponse(
        enhancedPrompt,
        res,
        llmModel,
        WEB_SEARCH_SYSTEM.SYSTEM_PROMPT,
        [], // No conversation history for web search
        {
          searchQuery: validation.query,
          searchResultsCount: processedResults.length,
          searchSources: processedResults.map(r => ({ title: r.extractedTitle, url: r.url })),
        },
        async (fullResponse) => {
          // Save the search query and response
          const messageData = {
            chatId: chat.id,
            message: `[WEB SEARCH] ${validation.query}`,
            response: fullResponse,
            llmModel: llmModel || process.env.DEFAULT_LLM_MODEL || 'gpt-3.5-turbo',
            attachmentPath: attachedFile?.path || null,
            attachmentName: attachedFile?.originalname || null,
            attachmentType: attachedFile?.mimetype || null,
            attachmentSize: attachedFile?.size || null,
            attachmentS3Url: attachedFile?.s3Url || null,
            attachmentS3Key: attachedFile?.s3Key || null,
            attachmentStorageType: attachedFile?.storageType || null,
            attachmentSecureId: attachedFile?.secureFileId || null,
          };

          const chatMessage = await ChatMessage.createMessage(messageData);

          // Deduct credits
          const creditDeducted = await CreditService.deductCredits(
            userId,
            CREDIT_SYSTEM.WEB_SEARCH_COST,
            CREDIT_SYSTEM.DESCRIPTIONS.WEB_SEARCH
          );

          if (!creditDeducted) {
            logger.error(`Failed to deduct credits for user ${userId} after web search`);
          }

          return {
            messageId: chatMessage.id,
            searchResultsCount: processedResults.length,
            searchSources: processedResults.map(r => ({ title: r.extractedTitle, url: r.url })),
          };
        },
        attachedFile
      );

      logger.info(`Processed web search for user ${userId}, session: ${chat.sessionId}`);

      return {
        response,
        sessionId: chat.sessionId,
        messageId: 'will-be-set-in-callback',
        searchResultsCount: processedResults.length,
      };
    } catch (error) {
      logger.error('Error processing user web search:', error);
      throw error;
    }
  }

  /**
   * Process web search with streaming response for guest users
   * @param {Object} searchRequest - Search request object
   * @param {Object} res - Express response object
   * @param {Object} [attachedFile] - Optional attached file
   * @param {string} clientIP - Client IP address
   * @returns {Promise<Object>} Search result with metadata
   */
  static async processGuestWebSearchStreaming(searchRequest, res, attachedFile = null, clientIP) {
    try {
      const { query, sessionId } = searchRequest;

      logger.info(`Processing web search for guest user, IP: ${clientIP}, query: "${query.substring(0, 50)}..."`);

      // Validate query
      const validation = validateSearchQuery(query);
      if (!validation.isValid) {
        throw new Error(validation.error);
      }

      // Get or create guest chat session
      let chat = await Chat.findBySessionId(sessionId);
      if (!chat) {
        chat = await Chat.createGuestChat({
          sessionId,
          clientIP,
          title: `Web Search: ${validation.query.substring(0, 50)}...`,
        });
      }

      // Set up streaming headers
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
      });

      // Send initial status
      res.write(`data: ${JSON.stringify({
        type: 'status',
        message: 'Starting web search...',
        timestamp: new Date().toISOString()
      })}\n\n`);

      // Perform web search
      const searchResults = await this.performSearch(validation.query);

      res.write(`data: ${JSON.stringify({
        type: 'status',
        message: `Found ${searchResults.length} search results, extracting content...`,
        timestamp: new Date().toISOString()
      })}\n\n`);

      // Process search results and extract content
      const processedResults = await processSearchResults(searchResults);

      res.write(`data: ${JSON.stringify({
        type: 'status',
        message: 'Storing search results in knowledge base...',
        timestamp: new Date().toISOString()
      })}\n\n`);

      // Store search results in Pinecone
      await this.storeSearchResultsInPinecone(sessionId, validation.query, processedResults);

      res.write(`data: ${JSON.stringify({
        type: 'status',
        message: 'Generating AI response...',
        timestamp: new Date().toISOString()
      })}\n\n`);

      // Format search results for LLM context
      const searchContext = formatSearchResultsForLLM(processedResults);

      // Create enhanced prompt with search context
      const enhancedPrompt = `${WEB_SEARCH_SYSTEM.SYSTEM_PROMPT}

Search Results:
${searchContext}

User Question: ${validation.query}

Please provide a comprehensive answer based on the search results above.`;

      // Generate streaming response using LLM
      const response = await LLMService.generateStreamingResponse(
        enhancedPrompt,
        res,
        null, // Use default model for guests
        WEB_SEARCH_SYSTEM.SYSTEM_PROMPT,
        [], // No conversation history for web search
        {
          searchQuery: validation.query,
          searchResultsCount: processedResults.length,
          searchSources: processedResults.map(r => ({ title: r.extractedTitle, url: r.url })),
        },
        async (fullResponse) => {
          // Save the search query and response
          const messageData = {
            chatId: chat.id,
            message: `[WEB SEARCH] ${validation.query}`,
            response: fullResponse,
            llmModel: process.env.DEFAULT_LLM_MODEL || 'gpt-3.5-turbo',
            attachmentPath: attachedFile?.path || null,
            attachmentName: attachedFile?.originalname || null,
            attachmentType: attachedFile?.mimetype || null,
            attachmentSize: attachedFile?.size || null,
            attachmentS3Url: attachedFile?.s3Url || null,
            attachmentS3Key: attachedFile?.s3Key || null,
            attachmentStorageType: attachedFile?.storageType || null,
            attachmentSecureId: attachedFile?.secureFileId || null,
          };

          const chatMessage = await ChatMessage.createMessage(messageData);

          return {
            messageId: chatMessage.id,
            searchResultsCount: processedResults.length,
            searchSources: processedResults.map(r => ({ title: r.extractedTitle, url: r.url })),
          };
        },
        attachedFile
      );

      logger.info(`Processed web search for guest user, session: ${chat.sessionId}`);

      return {
        response,
        sessionId: chat.sessionId,
        messageId: 'will-be-set-in-callback',
        searchResultsCount: processedResults.length,
      };
    } catch (error) {
      logger.error('Error processing guest web search:', error);
      throw error;
    }
  }

  /**
   * Store search results in Pinecone for RAG
   * @param {string} sessionId - Session ID
   * @param {string} query - Original search query
   * @param {Array} processedResults - Processed search results
   * @returns {Promise<void>}
   */
  static async storeSearchResultsInPinecone(sessionId, query, processedResults) {
    try {
      if (!PineconeService.isAvailable() || !processedResults.length) {
        logger.warn('Pinecone not available or no results to store');
        return;
      }

      logger.info(`Storing ${processedResults.length} search results in Pinecone for session ${sessionId}`);

      // Combine all search results into a single content string
      const combinedContent = processedResults.map((result, index) => {
        return `Source ${index + 1}: ${result.extractedTitle}
URL: ${result.url}
Content: ${result.fullContent}
---`;
      }).join('\n\n');

      // Store in Pinecone using session-based namespace
      await PineconeService.storeFileContent(
        combinedContent,
        {
          sessionId,
          fileName: `web_search_${Date.now()}.txt`,
          fileType: 'text/plain',
          originalQuery: query,
          searchResultsCount: processedResults.length,
          searchSources: processedResults.map(r => r.url),
          processingMethod: 'web_search_rag',
        }
      );

      logger.info(`Successfully stored web search results in Pinecone for session ${sessionId}`);
    } catch (error) {
      logger.error('Error storing search results in Pinecone:', error);
      // Don't throw error here as this is not critical for the search functionality
    }
  }

  /**
   * Get search history for a session
   * @param {string} sessionId - Session ID
   * @returns {Promise<Array>} Search history
   */
  static async getSearchHistory(sessionId) {
    try {
      const chat = await Chat.findBySessionId(sessionId);
      if (!chat) {
        return [];
      }

      const messages = await ChatMessage.findByChatId(chat.id);
      return messages
        .filter(msg => msg.message.startsWith('[WEB SEARCH]'))
        .map(msg => ({
          id: msg.id,
          query: msg.message.replace('[WEB SEARCH] ', ''),
          response: msg.response,
          createdAt: msg.createdAt,
        }));
    } catch (error) {
      logger.error('Error getting search history:', error);
      return [];
    }
  }
}
