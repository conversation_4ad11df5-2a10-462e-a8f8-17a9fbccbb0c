# Web Search Module Setup Guide

## Quick Start

### 1. Install Dependencies

The required dependencies have already been installed:
- `@langchain/tavily` - <PERSON><PERSON><PERSON><PERSON> Tavily integration
- `cheerio` - HTML parsing and content extraction
- `axios` - HTTP client for fetching web content

### 2. Environment Configuration

Add the following environment variable to your `.env` file:

```env
# Required for web search functionality
TAVILY_API_KEY=your_tavily_api_key_here

# Optional configurations
PINECONE_INDEX_NAME=theinfini-ai-chat
DEFAULT_LLM_MODEL=gpt-4o
```

### 3. Get Tavily API Key

1. Visit [Tavily.com](https://tavily.com/)
2. Sign up for a free account
3. Navigate to your dashboard
4. Copy your API key
5. Add it to your `.env` file

**Free Tier Limits:**
- 1,000 searches per month
- 3 results per search
- Basic search depth

### 4. Test the Setup

Run the test script to verify everything is working:

```bash
npm run test-web-search
```

This will test:
- Environment configuration
- Service initialization
- Search functionality
- Content extraction

## API Usage

### Basic Web Search

```bash
curl -X POST http://localhost:3000/api/websearch/query \
  -H "Content-Type: application/json" \
  -d '{
    "query": "latest developments in artificial intelligence 2024"
  }'
```

### Web Search with File Attachment

```bash
curl -X POST http://localhost:3000/api/websearch/query/attachment \
  -F "query=analyze this document with latest AI trends" \
  -F "file=@document.pdf"
```

### Check Service Status

```bash
curl http://localhost:3000/api/websearch/status
```

### Get Search History

```bash
curl http://localhost:3000/api/websearch/history \
  -H "Session-ID: your-session-id"
```

## Frontend Integration

### JavaScript/React Example

```javascript
import { useState, useEffect } from 'react';

function WebSearchComponent() {
  const [query, setQuery] = useState('');
  const [response, setResponse] = useState('');
  const [isSearching, setIsSearching] = useState(false);

  const performWebSearch = async () => {
    setIsSearching(true);
    setResponse('');

    try {
      const response = await fetch('/api/websearch/query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query }),
      });

      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              
              switch (data.type) {
                case 'status':
                  console.log('Status:', data.message);
                  break;
                case 'chunk':
                  setResponse(prev => prev + data.content);
                  break;
                case 'complete':
                  console.log('Search complete:', data.metadata);
                  setIsSearching(false);
                  break;
                case 'error':
                  console.error('Error:', data.error);
                  setIsSearching(false);
                  break;
              }
            } catch (e) {
              // Ignore parsing errors for incomplete chunks
            }
          }
        }
      }
    } catch (error) {
      console.error('Search failed:', error);
      setIsSearching(false);
    }
  };

  return (
    <div>
      <input
        type="text"
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        placeholder="Enter your search query..."
        disabled={isSearching}
      />
      <button onClick={performWebSearch} disabled={isSearching || !query}>
        {isSearching ? 'Searching...' : 'Search'}
      </button>
      
      {response && (
        <div style={{ marginTop: '20px', whiteSpace: 'pre-wrap' }}>
          {response}
        </div>
      )}
    </div>
  );
}

export default WebSearchComponent;
```

### Vue.js Example

```vue
<template>
  <div>
    <input
      v-model="query"
      type="text"
      placeholder="Enter your search query..."
      :disabled="isSearching"
      @keyup.enter="performWebSearch"
    />
    <button @click="performWebSearch" :disabled="isSearching || !query">
      {{ isSearching ? 'Searching...' : 'Search' }}
    </button>
    
    <div v-if="response" class="response">
      {{ response }}
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      query: '',
      response: '',
      isSearching: false
    };
  },
  methods: {
    async performWebSearch() {
      this.isSearching = true;
      this.response = '';

      try {
        const response = await fetch('/api/websearch/query', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ query: this.query }),
        });

        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6));
                
                if (data.type === 'chunk') {
                  this.response += data.content;
                } else if (data.type === 'complete') {
                  this.isSearching = false;
                } else if (data.type === 'error') {
                  console.error('Error:', data.error);
                  this.isSearching = false;
                }
              } catch (e) {
                // Ignore parsing errors
              }
            }
          }
        }
      } catch (error) {
        console.error('Search failed:', error);
        this.isSearching = false;
      }
    }
  }
};
</script>
```

## Configuration Options

### Web Search System Constants

You can modify the web search behavior by updating constants in `src/utils/constants.js`:

```javascript
const WEB_SEARCH_SYSTEM = {
  MAX_SEARCH_RESULTS: 3,           // Number of search results to fetch
  MAX_CONTENT_LENGTH: 5000,        // Max characters per result
  CONTENT_EXTRACTION_TIMEOUT: 10000, // Timeout for content extraction
  SEARCH_NAMESPACE_PREFIX: 'websearch', // Pinecone namespace prefix
  CHUNK_SIZE: 1000,                // Text chunk size for Pinecone
  SYSTEM_PROMPT: '...',            // System prompt for LLM
};
```

### Credit System

Web searches consume credits based on `CREDIT_SYSTEM.WEB_SEARCH_COST` (default: 3 credits per search).

## Troubleshooting

### Common Issues

1. **"Web search service is not available"**
   - Check if `TAVILY_API_KEY` is set in your `.env` file
   - Verify the API key is valid
   - Run `npm run test-web-search` to diagnose

2. **"Content extraction failed"**
   - Some websites block automated content extraction
   - The service will fallback to search snippets
   - Check network connectivity

3. **"Insufficient credits"**
   - User needs more credits to perform web search
   - Check credit balance with `/api/subscription/current`

4. **Slow response times**
   - Content extraction can take 5-10 seconds
   - Consider adjusting `CONTENT_EXTRACTION_TIMEOUT`
   - Monitor network latency

### Debug Mode

Enable debug logging by setting:

```env
LOG_LEVEL=debug
```

This will provide detailed logs for:
- Search query processing
- Content extraction attempts
- Pinecone storage operations
- LLM response generation

### Rate Limiting

If you encounter rate limiting:

1. **Tavily API limits**: Upgrade your Tavily plan
2. **Application rate limits**: Adjust limits in route definitions
3. **Content extraction timeouts**: Increase timeout values

## Production Considerations

### Performance

- Consider implementing search result caching
- Monitor Tavily API usage and costs
- Optimize content extraction timeouts
- Use CDN for static assets

### Security

- Validate and sanitize all search queries
- Implement proper rate limiting
- Monitor for abuse patterns
- Secure API keys and credentials

### Monitoring

- Track search success/failure rates
- Monitor content extraction performance
- Log Pinecone storage operations
- Alert on API quota limits

### Scaling

- Consider multiple Tavily API keys for higher limits
- Implement search result caching with Redis
- Use queue systems for batch processing
- Monitor and scale Pinecone usage
