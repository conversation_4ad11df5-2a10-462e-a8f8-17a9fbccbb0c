Web Search API Documentation
Overview
The Web Search API provides context-aware web search functionality that integrates real-time web results with AI-generated responses. All endpoints support both authenticated users and guest users with streaming responses.
Base URL
https://your-domain.com/api/websearch
Authentication
Authenticated Users: Include JWT token in Authorization header
Guest Users: No authentication required, but include Session-ID header
Session Management: All requests require a Session-ID header for chat continuity
Common Headers
Content-Type: application/json
Session-ID: your-session-id
Authorization: Bearer your-jwt-token (optional for guest users)
Endpoints
1. Web Search Query
   Perform a web search with streaming AI response.
   Endpoint: POST /api/websearch/query
   Request Body:
   {
   "query": "What are the latest developments in artificial intelligence 2024?",
   "llmModel": "gpt-4o"
   }
   Request Parameters:
   Parameter	Type	Required	Description
   query	string	Yes	Search query (3-500 characters)
   llmModel	string	No	LLM model to use (defaults to system default)
   Response: Server-Sent Events (SSE) stream
   Response Headers:
   Content-Type: text/event-stream
   Cache-Control: no-cache
   Connection: keep-alive
   Access-Control-Allow-Origin: *
   Response Events:
   Status Event:
   {
   "type": "status",
   "message": "Starting web search...",
   "timestamp": "2024-01-01T00:00:00.000Z"
   }
   Chunk Event:
   {
   "type": "chunk",
   "content": "Based on the latest search results, artificial intelligence has seen remarkable developments in 2024...",
   "timestamp": "2024-01-01T00:00:00.000Z"
   }
   Complete Event:
   {
   "type": "complete",
   "message": "Search completed successfully",
   "metadata": {
   "messageId": "msg_123456",
   "searchResultsCount": 3,
   "searchSources": [
   {
   "title": "AI Developments 2024",
   "url": "https://example.com/ai-2024"

Error Event:
{
"type": "error",
"error": "Insufficient credits for web search",
"timestamp": "2024-01-01T00:00:00.000Z"
}
Rate Limit: 10 requests per minute
2. Web Search with File Attachment
   Perform a web search combined with file attachment analysis.
   Endpoint: POST /api/websearch/query/attachment
   Request: Multipart form data
   Form Fields:
   Field	Type	Required	Description
   query	string	Yes	Search query (3-500 characters)
   llmModel	string	No	LLM model to use
   file	file	Yes	File attachment (PDF, DOC, TXT, etc.)
   Example Request:
   const formData = new FormData();
   formData.append('query', 'Analyze this document with latest AI trends');
   formData.append('file', fileInput.files[0]);
   formData.append('llmModel', 'gpt-4o');

fetch('/api/websearch/query/attachment', {
method: 'POST',
body: formData,
headers: {

Response: Same SSE stream format as web search query
Rate Limit: 5 requests per minute
3. Search History
   Get search history for the current session.
   Endpoint: GET /api/websearch/history
   Request Headers:
   Session-ID: your-session-id
   Authorization: Bearer your-jwt-token (optional)
   Response:
   {
   "success": true,
   "data": {
   "sessionId": "session_123456",
   "searchHistory": [
   {
   "id": "msg_123456",
   "query": "latest AI developments",
   "response": "Based on recent search results...",
   "createdAt": "2024-01-01T00:00:00.000Z"

Rate Limit: 20 requests per minute
4. Service Status
   Check web search service availability.
   Endpoint: GET /api/websearch/status
   Response:
   {
   "success": true,
   "data": {
   "webSearchAvailable": true,
   "tavilyConfigured": true,
   "maxResults": 3
   }
   }
   Rate Limit: 30 requests per minute
5. Simple Search (Testing)
   Perform a simple web search without streaming (for testing purposes).
   Endpoint: POST /api/websearch/simple
   Request Body:
   {
   "query": "test search query"
   }
   Response:
   {
   "success": true,
   "data": {
   "query": "test search query",
   "results": [
   {
   "title": "Search Result Title",
   "url": "https://example.com",
   "content": "Search result content...",
   "score": 0.95

Rate Limit: 15 requests per minute
Error Responses
Standard Error Format
{
"success": false,
"message": "Error description"
}
Common Error Codes
Status Code	Error	Description
400	Bad Request	Invalid query or missing required fields
401	Unauthorized	Invalid or expired JWT token
403	Forbidden	Insufficient credits or permissions
429	Too Many Requests	Rate limit exceeded
503	Service Unavailable	Web search service is down
500	Internal Server Error