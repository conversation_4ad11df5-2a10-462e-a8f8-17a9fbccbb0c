# Web Search Module Documentation

## Overview

The Web Search Module is a context-aware search tool that integrates LangChain's Tavily Search API with the existing RAG (Retrieval-Augmented Generation) pipeline. It allows users to perform real-time web searches, extract content from search results, store the information in Pinecone for context-aware responses, and stream AI-generated answers based on the search results.

## Features

- **Real-time Web Search**: Uses Tavily Search API for accurate, AI-optimized search results
- **Content Extraction**: Automatically extracts and cleans content from web pages
- **RAG Integration**: Stores search results in Pinecone for context-aware responses
- **Streaming Responses**: Provides real-time streaming responses like existing chat APIs
- **Multi-user Support**: Supports both authenticated users and guest users
- **File Attachment Support**: Allows combining web search with file attachments
- **Credit System Integration**: Integrates with the existing credit system
- **Search History**: Maintains search history for each session

## Architecture

### Components

1. **WebSearchService** (`src/services/WebSearchService.js`)
   - Core service handling web search operations
   - Integrates with Tavily Search API
   - Manages Pinecone storage and LLM response generation

2. **WebSearchController** (`src/controllers/WebSearchController.js`)
   - HTTP request handlers for web search endpoints
   - Handles both authenticated and guest user requests

3. **Web Search Utils** (`src/utils/webSearchUtils.js`)
   - Utility functions for content extraction and processing
   - HTML parsing and text cleaning
   - Search result formatting

4. **Routes** (`src/routes/webSearch.js`)
   - API endpoint definitions
   - Middleware integration
   - Request validation

## API Endpoints

### POST /api/websearch/query
Perform a web search with streaming AI response.

**Request Body:**
```json
{
  "query": "What are the latest developments in AI?",
  "llmModel": "gpt-4o" // optional
}
```

**Response:** Server-Sent Events (SSE) stream with:
- Status updates during search process
- Streaming AI response chunks
- Final completion with metadata

### POST /api/websearch/query/attachment
Perform a web search with file attachment support.

**Request:** Multipart form data with:
- `query`: Search query string
- `llmModel`: LLM model to use (optional)
- `file`: File attachment

**Response:** Same SSE stream format as above

### GET /api/websearch/history
Get search history for the current session.

**Response:**
```json
{
  "success": true,
  "data": {
    "sessionId": "session-id",
    "searchHistory": [
      {
        "id": "message-id",
        "query": "search query",
        "response": "AI response",
        "createdAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "count": 1
  }
}
```

### GET /api/websearch/status
Check web search service availability.

**Response:**
```json
{
  "success": true,
  "data": {
    "webSearchAvailable": true,
    "tavilyConfigured": true,
    "maxResults": 3
  }
}
```

### POST /api/websearch/simple
Perform a simple web search without streaming (for testing).

**Request Body:**
```json
{
  "query": "test query"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "query": "test query",
    "results": [...],
    "count": 3,
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
}
```

## Configuration

### Environment Variables

Required:
- `TAVILY_API_KEY`: API key for Tavily Search service
- `OPENAI_API_KEY`: For generating embeddings (existing)
- `PINECONE_API_KEY`: For storing search results (existing)

Optional:
- `PINECONE_INDEX_NAME`: Pinecone index name (default: 'theinfini-ai-chat')
- `DEFAULT_LLM_MODEL`: Default LLM model for responses

### Constants

Web search configuration is defined in `src/utils/constants.js`:

```javascript
const WEB_SEARCH_SYSTEM = {
  MAX_SEARCH_RESULTS: 3,
  MAX_CONTENT_LENGTH: 5000,
  CONTENT_EXTRACTION_TIMEOUT: 10000,
  SEARCH_NAMESPACE_PREFIX: 'websearch',
  CHUNK_SIZE: 1000,
  SYSTEM_PROMPT: '...',
};
```

## Usage Examples

### Frontend Integration

```javascript
// Perform web search with streaming response
const eventSource = new EventSource('/api/websearch/query', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    query: 'latest AI developments',
    llmModel: 'gpt-4o'
  })
});

eventSource.onmessage = (event) => {
  const data = JSON.parse(event.data);
  
  switch (data.type) {
    case 'status':
      console.log('Status:', data.message);
      break;
    case 'chunk':
      console.log('Response chunk:', data.content);
      break;
    case 'complete':
      console.log('Search complete:', data.metadata);
      eventSource.close();
      break;
    case 'error':
      console.error('Error:', data.error);
      eventSource.close();
      break;
  }
};
```

### Backend Service Usage

```javascript
import { WebSearchService } from './services/WebSearchService.js';

// Initialize service
await WebSearchService.initialize();

// Check availability
if (WebSearchService.isAvailable()) {
  // Perform search
  const results = await WebSearchService.performSearch('AI news');
  console.log(`Found ${results.length} results`);
}
```

## Credit System Integration

Web searches consume 3 credits per query (configurable in `CREDIT_SYSTEM.WEB_SEARCH_COST`). The system:

1. Checks user credits before performing search
2. Deducts credits after successful completion
3. Provides appropriate error messages for insufficient credits

## Rate Limiting

- Web search queries: 10 requests per minute
- Web search with attachments: 5 requests per minute
- Search history: 20 requests per minute
- Service status: 30 requests per minute
- Simple search: 15 requests per minute

## Error Handling

The module includes comprehensive error handling for:

- Invalid search queries
- Tavily API failures
- Content extraction timeouts
- Pinecone storage errors
- LLM response generation failures
- Credit system integration issues

## Testing

Run the test suite to verify functionality:

```bash
npm run test-web-search
```

The test script verifies:
- Environment configuration
- Service initialization
- Query validation
- Search functionality
- Content extraction
- Result processing

## Security Considerations

- Input validation for search queries
- Rate limiting to prevent abuse
- Content sanitization during extraction
- Secure file handling for attachments
- Session-based access control

## Performance Optimization

- Parallel content extraction from multiple URLs
- Intelligent content chunking for Pinecone storage
- Efficient HTML parsing with Cheerio
- Timeout handling for slow web requests
- Caching of search results in Pinecone

## Monitoring and Logging

The module provides detailed logging for:
- Search query processing
- Content extraction status
- Pinecone storage operations
- LLM response generation
- Error conditions and failures

## Future Enhancements

Potential improvements:
- Support for additional search engines
- Advanced content filtering and ranking
- Search result caching
- Multi-language support
- Image and video search capabilities
- Custom search domains and filters
