/**
 * @fileoverview Test script for Web Search integration without full server
 */

import dotenv from 'dotenv';
import { WebSearchService } from '../src/services/WebSearchService.js';
import { PineconeService } from '../src/services/PineconeService.js';
import { processSearchResults, formatSearchResultsForLLM } from '../src/utils/webSearchUtils.js';
import logger from '../src/config/logger.js';

// Load environment variables
dotenv.config();

/**
 * Test the complete web search integration flow
 */
async function testWebSearchIntegration() {
  console.log('🔍 Testing Web Search Integration Flow\n');

  try {
    // Step 1: Initialize services
    console.log('1. Initializing services...');
    await WebSearchService.initialize();
    await PineconeService.initialize();
    
    if (!WebSearchService.isAvailable()) {
      console.log('❌ Web Search Service not available');
      return;
    }
    
    if (!PineconeService.isAvailable()) {
      console.log('⚠️  Pinecone not available - will skip storage test');
    }
    
    console.log('✅ Services initialized');

    // Step 2: Perform search
    console.log('\n2. Performing web search...');
    const query = 'latest AI developments 2024';
    const searchResults = await WebSearchService.performSearch(query);
    console.log(`✅ Found ${searchResults.length} search results`);

    // Step 3: Process search results
    console.log('\n3. Processing search results...');
    const processedResults = await processSearchResults(searchResults);
    console.log(`✅ Processed ${processedResults.length} results with content`);

    // Step 4: Format for LLM
    console.log('\n4. Formatting results for LLM...');
    const formattedContext = formatSearchResultsForLLM(processedResults);
    console.log(`✅ Generated ${formattedContext.length} characters of context`);
    console.log(`   Preview: ${formattedContext.substring(0, 200)}...`);

    // Step 5: Test Pinecone storage (if available)
    if (PineconeService.isAvailable()) {
      console.log('\n5. Testing Pinecone storage...');
      const testSessionId = `test_session_${Date.now()}`;
      
      try {
        await WebSearchService.storeSearchResultsInPinecone(
          testSessionId,
          query,
          processedResults
        );
        console.log('✅ Successfully stored search results in Pinecone');
        
        // Test retrieval
        const retrievedContext = await PineconeService.searchRelevantContent(
          testSessionId,
          query,
          3
        );
        console.log(`✅ Retrieved ${retrievedContext.length} relevant chunks from Pinecone`);
        
      } catch (pineconeError) {
        console.log(`⚠️  Pinecone storage test failed: ${pineconeError.message}`);
      }
    }

    // Step 6: Test validation
    console.log('\n6. Testing validation...');
    const testQueries = [
      { query: '', shouldPass: false },
      { query: 'hi', shouldPass: false },
      { query: 'valid search query', shouldPass: true },
      { query: 'A'.repeat(600), shouldPass: false }
    ];

    for (const { query, shouldPass } of testQueries) {
      try {
        await WebSearchService.performSearch(query);
        console.log(`   Query "${query.substring(0, 20)}...": ${shouldPass ? '✅' : '❌'} (Expected: ${shouldPass ? 'pass' : 'fail'})`);
      } catch (error) {
        console.log(`   Query "${query.substring(0, 20)}...": ${shouldPass ? '❌' : '✅'} (Expected: ${shouldPass ? 'pass' : 'fail'})`);
      }
    }

    console.log('\n🎉 Web Search Integration test completed successfully!');

  } catch (error) {
    console.error('❌ Integration test failed:', error);
    process.exit(1);
  }
}

/**
 * Test the web search controller logic (without HTTP)
 */
async function testControllerLogic() {
  console.log('\n🎮 Testing Controller Logic\n');

  try {
    // Simulate request/response objects
    const mockReq = {
      body: {
        query: 'test search query',
        llmModel: 'gpt-4o'
      },
      sessionId: `test_session_${Date.now()}`,
      user: null, // Guest user
      clientIP: '127.0.0.1'
    };

    const mockRes = {
      headersSent: false,
      writeHead: (status, headers) => {
        console.log(`   Response headers set: ${status}`);
      },
      write: (data) => {
        try {
          const parsed = JSON.parse(data.replace('data: ', '').trim());
          if (parsed.type === 'status') {
            console.log(`   Status: ${parsed.message}`);
          } else if (parsed.type === 'chunk') {
            console.log(`   Chunk: ${parsed.content.substring(0, 50)}...`);
          } else if (parsed.type === 'complete') {
            console.log(`   Complete: ${JSON.stringify(parsed.metadata)}`);
          }
        } catch (e) {
          // Ignore parsing errors for incomplete chunks
        }
      },
      end: () => {
        console.log('   Response ended');
      }
    };

    console.log('1. Testing guest user web search flow...');
    
    // This would normally call WebSearchController.searchQuery
    // But we'll simulate the core logic here
    const validation = { isValid: true, query: mockReq.body.query };
    
    if (validation.isValid) {
      console.log('✅ Query validation passed');
      
      const searchResults = await WebSearchService.performSearch(validation.query);
      console.log(`✅ Search completed: ${searchResults.length} results`);
      
      const processedResults = await processSearchResults(searchResults.slice(0, 2));
      console.log(`✅ Content extraction completed: ${processedResults.length} processed`);
      
      console.log('✅ Controller logic test passed');
    }

  } catch (error) {
    console.error('❌ Controller logic test failed:', error);
  }
}

/**
 * Main test function
 */
async function main() {
  console.log('🚀 Web Search Integration Test Suite\n');
  console.log('='.repeat(60));

  // Test core integration
  await testWebSearchIntegration();

  // Test controller logic
  await testControllerLogic();

  console.log('\n' + '='.repeat(60));
  console.log('✨ All integration tests completed!');
}

// Run the tests
main().catch(error => {
  console.error('💥 Integration test suite failed:', error);
  process.exit(1);
});
